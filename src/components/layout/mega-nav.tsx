"use client";

import { FC, useEffect, useRef } from "react";
import { createPortal } from "react-dom";
import gsap from "gsap";
import { CMSLink } from "../cms-link";
import type { Header } from "@/payload-types";

interface MegaNavProps {
	item: Header["navItems"][number];
	onClose: () => void;
}

export const MegaNav: FC<MegaNavProps> = ({ item, onClose }) => {
	const overlayRef = useRef<HTMLDivElement>(null);

	// GSAP animate in on mount
	useEffect(() => {
		const ctx = gsap.context(() => {
			gsap.fromTo(
				overlayRef.current,
				{ yPercent: 100, opacity: 0 },
				{ yPercent: 0, opacity: 1, duration: 0.5, ease: "power3.out" }
			);
		});

		return () => {
			gsap.to(overlayRef.current, {
				yPercent: 100,
				opacity: 0,
				duration: 0.4,
				ease: "power2.in",
			});
		};
	}, []);

	return createPortal(
		<div
			ref={overlayRef}
			className="fixed inset-0 z-40 bg-white text-black p-10 overflow-y-auto"
		>
			<div className="flex justify-between items-start mb-10">
				<h2 className="text-xl font-bold">{item.link.label}</h2>
				<button
					onClick={onClose}
					className="text-sm underline"
				>
					Close
				</button>
			</div>

			<div className="grid grid-cols-2 gap-6">
				{item.children?.map((child) => (
					<div key={child.id} className="text-base">
						<CMSLink {...child.link} />
					</div>
				))}
			</div>
		</div>,
		document.body
	);
};
