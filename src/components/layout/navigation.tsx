"use client";

import type { <PERSON> } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import type { Head<PERSON>, <PERSON> } from "@/payload-types";
import { CMSLink } from "../cms-link";

interface INavigationProps {
	header: Header;
}

export const Navigation: FC<INavigationProps> = ({ header }) => {
	const router = useRouter();

	return (
		<header className="fixed inset-x-0 top-0 z-50 mix-blend-difference text-primary layout-block">
			<div className="default-grid py-safe">
				<CMSLink
					type="reference"
					reference={{ relationTo: "pages", value: { slug: "home" } as Page }}
					className="relative col-span-4 cursor-pointer"
				>
					<span className="inline-block bg-[#673DC3] px-1 text-sm">
						HARD CHOR
					</span>
				</CMSLink>
				<nav className="col-span-8 col-start-5 flex justify-between">
					<ul className="flex items-center gap-x-[calc(var(--gap)/2)]">
						{header.navItems?.map((item) => {
							if (item.isMegaNav) return <li>mega nav</li>;

							return (
								<li key={item.id} className="text-sm">
									<CMSLink {...item.link} />
								</li>
							);
						})}
					</ul>
					<Link href={"/contact"} className="text-sm">
						Get Started
					</Link>
				</nav>
			</div>
		</header>
	);
};
